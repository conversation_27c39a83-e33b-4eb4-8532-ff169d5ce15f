import React, { useState, useEffect } from 'react';
import { ActivityIndicator, Modal, TextInput } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { router, useLocalSearchParams } from 'expo-router';
import { StyledText, StyledView, StyledTouchableOpacity, StyledScrollView, StyledSafeAreaView } from '@/components/ui/StyledComponents';
import { HeaderText, SubtitleText } from '@/components/ui/Typography';
import { Feather } from '@expo/vector-icons';

interface BankAccount {
  id: string;
  bankName: string;
  iban: string;
  monthlyIncome: number;
  averageIncome: number;
  incomeStability: number;
  lastPayment: string;
  paymentFrequency: string;
}

interface IncomeData {
  accounts: BankAccount[];
  totalMonthlyIncome: number;
  totalAverageIncome: number;
  overallStability: number;
}

// Utility function to mask IBAN for security
const maskIban = (iban: string): string => {
  if (iban.length < 8) return iban;
  const start = iban.substring(0, 4);
  const end = iban.substring(iban.length - 4);
  const middle = '*'.repeat(Math.max(0, iban.length - 8));
  return `${start} ${middle} ${end}`;
};

export default function IncomeAnalysis() {
  const { country, bank } = useLocalSearchParams<{ country: string; bank: string }>();
  const [isAnalyzing, setIsAnalyzing] = useState(true);
  const [incomeData, setIncomeData] = useState<IncomeData | null>(null);
  const [isConfirmed, setIsConfirmed] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showReportModal, setShowReportModal] = useState(false);
  const [reportSubmitted, setReportSubmitted] = useState(false);
  const [showThankYouModal, setShowThankYouModal] = useState(false);
  const [reportText, setReportText] = useState('');

  // Mock analysis process
  useEffect(() => {
    const analyzeIncome = async () => {
      // Simulate analysis delay
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Mock income data with multiple accounts
      const mockAccounts: BankAccount[] = [
        {
          id: '1',
          bankName: bank || 'Chase Bank',
          iban: '**********************',
          monthlyIncome: 5000,
          averageIncome: 4850,
          incomeStability: 95,
          lastPayment: '2024-01-15',
          paymentFrequency: 'Bi-weekly'
        }
      ];

      setIncomeData({
        accounts: mockAccounts,
        totalMonthlyIncome: mockAccounts.reduce((sum, acc) => sum + acc.monthlyIncome, 0),
        totalAverageIncome: mockAccounts.reduce((sum, acc) => sum + acc.averageIncome, 0),
        overallStability: Math.round(mockAccounts.reduce((sum, acc) => sum + acc.incomeStability, 0) / mockAccounts.length)
      });

      setIsAnalyzing(false);
    };

    analyzeIncome();
  }, [bank]);

  const handleContinue = async () => {
    if (!isConfirmed) return;

    setIsLoading(true);

    try {
      router.push({
        pathname: '/(auth)/income-verification/banking/score-card',
        params: {
          country,
          bank,
          monthlyIncome: incomeData?.totalMonthlyIncome?.toString() || '0',
          averageIncome: incomeData?.totalAverageIncome?.toString() || '0',
          incomeStability: incomeData?.overallStability?.toString() || '0'
        }
      });
    } catch (error) {
      console.error('Navigation error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleBack = () => {
    router.back();
  };

  const handleAddAnotherAccount = () => {
    // Navigate to standalone income verification method selection while preserving existing data
    router.push('/(auth)/income-verification');
  };

  const handleReportIncorrectData = () => {
    setShowReportModal(true);
  };

  const handleSubmitReport = () => {
    if (!reportText.trim()) return;

    // Log the report for development purposes
    console.log('Income data issue reported for manual review:', reportText);

    // Close report modal and show thank you modal
    setShowReportModal(false);
    setShowThankYouModal(true);
    setReportSubmitted(true);
  };

  const handleCancelReport = () => {
    setShowReportModal(false);
    setReportText('');
  };

  const handleCloseThankYou = () => {
    setShowThankYouModal(false);
  };

  const canProceed = (): boolean => {
    return !isAnalyzing && isConfirmed;
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  if (isAnalyzing) {
    return (
      <StyledSafeAreaView className="flex-1 bg-white">
        <StatusBar style="dark" />

        <StyledScrollView className="flex-grow px-6 pb-10">
          <StyledView className="w-full max-w-md mx-auto flex-1">
            {/* Back button */}
            <StyledView className="mb-6 mt-4">
              <StyledTouchableOpacity
                onPress={handleBack}
                className="p-2 self-start"
              >
                <Feather name="arrow-left" size={24} color="#374151" />
              </StyledTouchableOpacity>
            </StyledView>

          {/* Loading Content */}
          <StyledView className="flex-1 justify-center items-center">
            <ActivityIndicator size="large" color="#4ca2f5" />
            <StyledText className="text-2xl font-bold text-gray-900 mt-6 mb-4 text-center">
              Analyzing Your Income
            </StyledText>
            <StyledText className="text-lg text-gray-600 text-center leading-relaxed mb-8">
              We're securely analyzing 3 months of bank statement data to verify your income
            </StyledText>

            {/* Progress Steps */}
            <StyledView className="w-full max-w-sm">
              <StyledView className="flex-row items-center mb-3">
                <Feather name="check-circle" size={20} color="#10b981" />
                <StyledText className="text-green-700 ml-3">Connected to bank account</StyledText>
              </StyledView>
              <StyledView className="flex-row items-center mb-3">
                <ActivityIndicator size="small" color="#4ca2f5" />
                <StyledText className="text-blue-600 ml-3">Analyzing transaction data...</StyledText>
              </StyledView>
              <StyledView className="flex-row items-center">
                <StyledView className="w-5 h-5 border-2 border-gray-300 rounded-full mr-3" />
                <StyledText className="text-gray-500 ml-3">Generating income summary</StyledText>
              </StyledView>
            </StyledView>
          </StyledView>
        </StyledView>
      </StyledScrollView>
    </StyledSafeAreaView>
  );
}

return (
    <StyledSafeAreaView className="flex-1 bg-white">
      <StatusBar style="dark" />

      <StyledScrollView className="flex-grow px-6 pb-10">
        <StyledView className="w-full max-w-md mx-auto flex-1">
          {/* Back button */}
          <StyledView className="mb-6 mt-4">
            <StyledTouchableOpacity
              onPress={handleBack}
              className="p-2 self-start"
              disabled={isLoading}
            >
              <Feather name="arrow-left" size={24} color="#374151" />
            </StyledTouchableOpacity>
          </StyledView>

          {/* Step content */}
          <StyledView className="flex-1">
            <StyledView className="mb-12">
              <HeaderText>Income Summary</HeaderText>
              <SubtitleText>Here's what we found from your bank statements</SubtitleText>
            </StyledView>



            {/* Connected Accounts */}
            <StyledView className="mb-8">
              <StyledText className="text-lg font-semibold text-gray-900 mb-4">Connected Bank Accounts</StyledText>

              {incomeData?.accounts.map((account) => (
                <StyledView key={account.id} className="bg-white border border-gray-200 rounded-xl p-6 mb-4 shadow-sm">
                  <StyledView className="mb-4">
                    <StyledText className="text-base font-semibold text-gray-900">{account.bankName}</StyledText>
                    <StyledText className="text-sm text-gray-500">{maskIban(account.iban)}</StyledText>
                  </StyledView>

                  <StyledView className="flex-row justify-between">
                    <StyledText className="text-sm text-gray-600">3-Month Average</StyledText>
                    <StyledText className="text-sm font-semibold text-gray-900">{formatCurrency(account.averageIncome)}</StyledText>
                  </StyledView>
                </StyledView>
              ))}
            </StyledView>



            {/* Add Another Bank Account Button */}
            <StyledView className="mb-8">
              <StyledTouchableOpacity
                className="bg-white border-2 border-secondary rounded-xl py-4 px-6 items-center shadow-sm"
                onPress={handleAddAnotherAccount}
                disabled={isLoading}
              >
                <StyledView className="flex-row items-center">
                  <Feather name="plus" size={20} color="#4ca2f5" />
                  <StyledText className="text-base font-semibold text-secondary ml-2">
                    Add Another Bank Account
                  </StyledText>
                </StyledView>
                <StyledText className="text-sm text-gray-600 mt-1 text-center">
                  Connect multiple accounts for complete income verification
                </StyledText>
              </StyledTouchableOpacity>
            </StyledView>

            {/* Confirmation Checkbox */}
            <StyledView className="mb-8 pt-6 border-t border-gray-200">
              <StyledTouchableOpacity
                className="w-full py-3 items-center flex-row"
                onPress={() => setIsConfirmed(!isConfirmed)}
                disabled={isLoading}
                style={{ minHeight: 44 }}
              >
                <StyledView
                  className={`w-5 h-5 border-2 rounded mr-3 items-center justify-center ${
                    isConfirmed
                      ? 'border-secondary bg-secondary'
                      : 'border-gray-300 bg-white'
                  }`}
                >
                  {isConfirmed && (
                    <Feather name="check" size={14} color="white" />
                  )}
                </StyledView>
                <StyledText className="text-gray-600 text-base flex-1">
                  I confirm this income information is accurate
                </StyledText>
              </StyledTouchableOpacity>
            </StyledView>
          </StyledView>

          {/* Continue Button */}
          <StyledTouchableOpacity
            className={`rounded-xl py-4 px-6 items-center w-full shadow-md transition-colors ${
              isLoading || !canProceed()
                ? 'bg-gray-300 opacity-70'
                : 'bg-secondary hover:bg-secondary/90'
            }`}
            onPress={handleContinue}
            disabled={isLoading || !canProceed()}
          >
            {isLoading ? (
              <ActivityIndicator color="white" size="small" />
            ) : (
              <StyledText className={`text-base font-semibold ${
                !canProceed() ? 'text-gray-500' : 'text-white'
              }`}>
                Continue
              </StyledText>
            )}
          </StyledTouchableOpacity>

          {/* Report Incorrect Income Data Link */}
          <StyledView className="mt-6 items-center">
            <StyledTouchableOpacity
              className="py-3 px-4 items-center"
              onPress={handleReportIncorrectData}
              disabled={isLoading || reportSubmitted}
              style={{ minHeight: 44 }}
            >
              <StyledView className="flex-row items-center">
                <Feather
                  name="alert-triangle"
                  size={14}
                  color={reportSubmitted ? "#9CA3AF" : "#4ca2f5"}
                />
                <StyledText className={`text-sm ml-2 ${
                  reportSubmitted
                    ? 'text-gray-400'
                    : 'text-secondary underline'
                }`}>
                  {reportSubmitted ? 'Review Request Submitted' : 'Report Incorrect Income Data'}
                </StyledText>
              </StyledView>
            </StyledTouchableOpacity>
          </StyledView>
        </StyledView>
      </StyledScrollView>

      {/* Report Issue Modal */}
      <Modal
        visible={showReportModal}
        transparent={true}
        animationType="fade"
        onRequestClose={handleCancelReport}
      >
        <StyledView className="flex-1 bg-black/50 items-center justify-center p-6">
          <StyledView className="bg-white rounded-xl p-6 w-full max-w-sm mx-auto shadow-xl">
            {/* Modal Header */}
            <StyledView className="mb-4">
              <StyledText className="text-xl font-bold text-gray-900 mb-2">
                Report Income Data Issue
              </StyledText>
              <StyledText className="text-sm text-gray-600 leading-relaxed">
                Our support team will manually review your income information within 1-2 business days. You'll receive an email update once the review is complete.
              </StyledText>
            </StyledView>

            {/* Text Input Field */}
            <StyledView className="mb-4">
              <TextInput
                className="border border-gray-300 rounded-xl p-4 bg-white text-gray-900 text-base"
                placeholder="Please describe what's incorrect about your income data..."
                placeholderTextColor="#9B9B9B"
                value={reportText}
                onChangeText={(text) => {
                  if (text.length <= 500) {
                    setReportText(text);
                  }
                }}
                multiline
                numberOfLines={4}
                textAlignVertical="top"
                style={{ minHeight: 100 }}
                maxLength={500}
              />
              <StyledView className="flex-row justify-between items-center mt-2">
                <StyledText className="text-xs text-gray-500">
                  Help us understand what needs to be corrected
                </StyledText>
                <StyledText className={`text-xs ${reportText.length > 450 ? 'text-red-500' : 'text-gray-400'}`}>
                  {reportText.length}/500
                </StyledText>
              </StyledView>
            </StyledView>

            {/* Modal Actions */}
            <StyledView className="space-y-3">
              {/* Submit Report Button */}
              <StyledTouchableOpacity
                className={`rounded-xl py-4 px-6 items-center shadow-sm ${
                  reportText.trim().length > 0
                    ? 'bg-secondary'
                    : 'bg-gray-300'
                }`}
                onPress={handleSubmitReport}
                disabled={reportText.trim().length === 0}
              >
                <StyledText className={`text-base font-semibold ${
                  reportText.trim().length > 0 ? 'text-white' : 'text-gray-500'
                }`}>
                  Submit Report
                </StyledText>
              </StyledTouchableOpacity>

              {/* Cancel Button */}
              <StyledTouchableOpacity
                className="bg-white border-2 border-gray-300 rounded-xl py-4 px-6 items-center"
                onPress={handleCancelReport}
              >
                <StyledText className="text-gray-700 text-base font-semibold">
                  Cancel
                </StyledText>
              </StyledTouchableOpacity>
            </StyledView>
          </StyledView>
        </StyledView>
      </Modal>

      {/* Thank You Modal */}
      <Modal
        visible={showThankYouModal}
        transparent={true}
        animationType="fade"
        onRequestClose={handleCloseThankYou}
      >
        <StyledView className="flex-1 bg-black/50 items-center justify-center p-6">
          <StyledView className="bg-white rounded-xl p-6 w-full max-w-sm mx-auto shadow-xl">
            {/* Thank You Header */}
            <StyledView className="items-center mb-6">
              <StyledView className="w-16 h-16 bg-green-100 rounded-full items-center justify-center mb-4">
                <Feather name="check" size={32} color="#10b981" />
              </StyledView>
              <StyledText className="text-xl font-bold text-gray-900 mb-2 text-center">
                Thank You for Your Feedback
              </StyledText>
            </StyledView>

            {/* Thank You Content */}
            <StyledView className="mb-6">
              <StyledText className="text-sm text-gray-600 leading-relaxed text-center mb-4">
                Your report has been submitted successfully. You can close this window now. Our support team will review your income information and get back to you within 1-2 business days.
              </StyledText>
              <StyledView className="bg-blue-50 border border-blue-200 rounded-xl p-4">
                <StyledText className="text-sm text-blue-800 leading-relaxed text-center">
                  Your account registration information has been saved. Once we've verified your income data, you'll only need to confirm the final account setup step - simple and easy!
                </StyledText>
              </StyledView>
            </StyledView>

            {/* Close Button */}
            <StyledTouchableOpacity
              className="bg-secondary rounded-xl py-4 px-6 items-center shadow-sm"
              onPress={handleCloseThankYou}
            >
              <StyledText className="text-white text-base font-semibold">
                Close
              </StyledText>
            </StyledTouchableOpacity>
          </StyledView>
        </StyledView>
      </Modal>
    </StyledSafeAreaView>
  );
}
