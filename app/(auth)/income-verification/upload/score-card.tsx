import React, { useState } from 'react';
import { ActivityIndicator } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { router, useLocalSearchParams } from 'expo-router';
import { useAuth } from '@/contexts/AuthContext';
import { StyledText, StyledView, StyledTouchableOpacity, StyledScrollView, StyledSafeAreaView } from '@/components/ui/StyledComponents';
import { HeaderText, SubtitleText } from '@/components/ui/Typography';
import { ScoreCard } from '@/components/ui/ScoreCard';
import { Feather } from '@expo/vector-icons';

export default function UploadScoreCard() {
  const params = useLocalSearchParams();
  const { state } = useAuth();
  const [isLoading, setIsLoading] = useState(false);

  const handleContinue = async () => {
    setIsLoading(true);

    try {
      // Check if user is already authenticated
      if (state.isAuthenticated) {
        // For authenticated users (like <EMAIL>), navigate to dashboard
        router.replace('/(dashboard)');
      } else {
        // For new users in registration flow, continue with registration
        router.replace({
          pathname: '/(auth)/register',
          params: { incomeVerificationCompleted: 'true', method: 'upload' }
        });
      }
    } catch (error) {
      console.error('Navigation error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleBack = () => {
    router.back();
  };



  // Mock tenant data based on the income verification (slightly lower score than banking due to manual review)
  const tenantData = {
    tenantName: 'Olivia Smith',
    tenantScore: 92, // Slightly lower than banking verification
    monthlyIncome: parseInt(params.monthlyIncome as string) || 4800,
    maxRent: Math.round((parseInt(params.monthlyIncome as string) || 4800) * 0.4),
    verifications: {
      identity: true,
      income: true, // Now verified through document upload
      phone: true,
      email: true,
    },
    details: {
      pets: 'No pets',
      age: '1990s',
      languages: 'English, French',
      location: 'London, UK',
    },
  };

  return (
    <StyledSafeAreaView className="flex-1 bg-white">
      <StatusBar style="dark" />

      <StyledScrollView className="flex-grow px-6 pb-10">
        <StyledView className="w-full max-w-md mx-auto flex-1">
          {/* Back button */}
          <StyledView className="mb-6 mt-4">
            <StyledTouchableOpacity
              onPress={handleBack}
              className="p-2 self-start"
              disabled={isLoading}
            >
              <Feather name="arrow-left" size={24} color="#374151" />
            </StyledTouchableOpacity>
          </StyledView>

          {/* Step content */}
          <StyledView className="flex-1">
            <StyledView className="mb-8">
              <HeaderText>Your Tenant Score</HeaderText>
              <SubtitleText>Below you see an overview of your tenant scorecard that you can use when applying on CasaPay</SubtitleText>
            </StyledView>

            {/* Processing Notice */}
            <StyledView className="bg-blue-50 border border-blue-200 rounded-xl p-4 mb-6">
              <StyledView className="flex-row items-start">
                <Feather name="info" size={20} color="#4ca2f5" />
                <StyledView className="ml-3 flex-1">
                  <StyledText className="text-sm font-medium text-blue-900 mb-1">
                    Document Review Process
                  </StyledText>
                  <StyledText className="text-sm text-blue-700">
                    Your documents have been processed using secure automated analysis. Final verification may take 1-2 business days for complete accuracy.
                  </StyledText>
                </StyledView>
              </StyledView>
            </StyledView>

            {/* Score Card */}
            <StyledView className="mb-8">
              <ScoreCard
                {...tenantData}
              />
            </StyledView>

          </StyledView>

          {/* Continue Button */}
          <StyledTouchableOpacity
            className={`rounded-xl py-4 px-6 items-center w-full shadow-md transition-colors ${
              isLoading
                ? 'bg-gray-300 opacity-70'
                : 'bg-secondary hover:bg-secondary/90'
            }`}
            onPress={handleContinue}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator color="white" size="small" />
            ) : (
              <StyledText className="text-white text-base font-semibold">
                {state.isAuthenticated ? 'Go to Dashboard' : 'Complete Registration'}
              </StyledText>
            )}
          </StyledTouchableOpacity>
        </StyledView>
      </StyledScrollView>
    </StyledSafeAreaView>
  );
}
